document.addEventListener('DOMContentLoaded', () => {
    // Load subscription and transaction details
    loadConfirmationDetails();

    // Handle receipt download
    const downloadButton = document.querySelector('.download-receipt');
    if (downloadButton) {
        downloadButton.addEventListener('click', generateReceipt);
    }
});

async function loadConfirmationDetails() {
    // Get data from session storage
    const subscriptionId = sessionStorage.getItem('subscriptionId');
    const transactionId = sessionStorage.getItem('transactionId');
    const nextBillingDate = sessionStorage.getItem('nextBillingDate');
    const selectedPlanJson = sessionStorage.getItem('selectedPlan');

    if (!selectedPlanJson) {
        // If no plan is selected, redirect to subscription page
        window.location.href = 'subscription.html';
        return;
    }

    try {
        // Parse selected plan
        const selectedPlan = JSON.parse(selectedPlanJson);

        // Fallback: mock subscription details if no API
        let subscriptionDetails = { currentPeriodEnd: nextBillingDate };
        // If you have an API, you can use it here
        /*
        if (subscriptionId && typeof API !== 'undefined') {
            subscriptionDetails = await API.getSubscriptionDetails(subscriptionId);
        }
        */

        // Update UI with subscription details
        const planNameElement = document.getElementById('planName');
        const billingCycleElement = document.getElementById('billingCycle');
        const nextBillingElement = document.getElementById('nextBilling');
        const amountElement = document.getElementById('amount');
        const userEmailElement = document.getElementById('userEmail');
        const pageTitleElement = document.querySelector('h1');

        if (planNameElement) planNameElement.textContent = selectedPlan.name;
        if (billingCycleElement) {
            billingCycleElement.textContent = selectedPlan.interval === 'month' ? 'Monthly' : 'Yearly';
        }
        // Format next billing date
        if (nextBillingElement) {
            const nextBillingDateObj = new Date(nextBillingDate || subscriptionDetails.currentPeriodEnd);
            nextBillingElement.textContent = nextBillingDateObj.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
        // Update amount
        if (amountElement) {
            amountElement.textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
        }
        // Update user email (in a real app, this would come from the user's profile)
        if (userEmailElement) {
            const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
            const user = userJson ? JSON.parse(userJson) : { email: '<EMAIL>' };
            userEmailElement.textContent = user.email;
        }
        // Update page title based on plan
        if (pageTitleElement) {
            pageTitleElement.textContent = `Welcome to Banshee ${selectedPlan.name}!`;
        }
        // Update features based on plan name (use .name, not .id)
        updateFeaturesList(selectedPlan.name ? selectedPlan.name.toLowerCase() : 'premium');
    } catch (error) {
        console.error('Error loading confirmation details:', error);
        showToast('There was an error loading your subscription details.', 'error');
    }
}

function updateFeaturesList(planKey) {
    // This would be more dynamic in a real application
    const featuresList = document.querySelector('.feature-list');

    if (!featuresList) return;

    // Clear existing features
    featuresList.innerHTML = '';

    // Add features based on plan
    const features = [];
    if (planKey === 'free account' || planKey === 'free') {
        features.push(
            { icon: 'music', text: 'Basic music streaming with ads' },
            { icon: 'headphones', text: 'Standard audio quality' },
            { icon: 'mobile-alt', text: 'Mobile app access' }
        );
    } else if (planKey === 'premium') {
        features.push(
            { icon: 'music', text: 'Enjoy ad-free music streaming' },
            { icon: 'download', text: 'Download unlimited songs for offline listening' },
            { icon: 'headphones', text: 'Experience HD audio quality' },
            { icon: 'mobile-alt', text: 'Listen on any device' }
        );
    } else if (planKey === 'artist account' || planKey === 'artist') {
        features.push(
            { icon: 'music', text: 'All Premium features included' },
            { icon: 'upload', text: 'Upload unlimited tracks' },
            { icon: 'chart-line', text: 'Access advanced analytics dashboard' },
            { icon: 'bullhorn', text: 'Use promotional tools' }
        );
    }

    // Add features to list
    features.forEach(feature => {
        const li = document.createElement('li');
        li.innerHTML = `
            <i class="fas fa-${feature.icon}"></i>
            <span>${feature.text}</span>
        `;
        featuresList.appendChild(li);
    });
}

function generateReceipt() {
    // Implement receipt generation logic here
    // Could use PDF.js or similar library to generate a PDF receipt
    console.log('Generating receipt...');
    // For now, just show a toast message
    showToast('Receipt downloaded successfully!');
}

function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}