// Create Playlist Management System
class CreatePlaylistManager {
    constructor() {
        this.form = null;
        this.coverFile = null;
        this.isSubmitting = false;
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.setupValidation();
    }

    bindElements() {
        this.form = document.getElementById('createPlaylistForm');
        this.nameInput = document.getElementById('playlistName');
        this.descInput = document.getElementById('playlistDescription');
        this.privacySelect = document.getElementById('playlistPrivacy');
        this.genreSelect = document.getElementById('playlistGenre');
        this.moodSelect = document.getElementById('playlistMood');
        this.coverUpload = document.getElementById('coverUpload');
        this.coverPreview = document.getElementById('coverPreview');
        this.generateCoverBtn = document.getElementById('generateCoverBtn');
        this.cancelBtn = document.getElementById('cancelBtn');
        this.createBtn = this.form.querySelector('.create-btn');
        this.successMessage = document.getElementById('form-success');
        this.errorMessage = document.getElementById('form-error');
        this.errorText = document.getElementById('errorText');
        this.liveRegion = document.getElementById('aria-live-region');
        this.nameCount = document.getElementById('nameCount');
        this.descCount = document.getElementById('descCount');
    }

    bindEvents() {
        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // Character counting
        this.nameInput.addEventListener('input', () => {
            this.updateCharacterCount(this.nameInput, this.nameCount, 50);
            this.validateField(this.nameInput);
        });

        this.descInput.addEventListener('input', () => {
            this.updateCharacterCount(this.descInput, this.descCount, 200);
        });

        // Cover upload
        this.coverUpload.addEventListener('change', (e) => {
            this.handleCoverUpload(e);
        });

        // Generate cover
        this.generateCoverBtn.addEventListener('click', () => {
            this.generateCover();
        });

        // Cancel button
        this.cancelBtn.addEventListener('click', () => {
            this.handleCancel();
        });

        // Real-time validation
        this.nameInput.addEventListener('blur', () => {
            this.validateField(this.nameInput);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.handleSubmit();
            }
            if (e.key === 'Escape') {
                this.handleCancel();
            }
        });
    }

    setupValidation() {
        // Initialize character counts
        this.updateCharacterCount(this.nameInput, this.nameCount, 50);
        this.updateCharacterCount(this.descInput, this.descCount, 200);
    }

    updateCharacterCount(input, countElement, maxLength) {
        const currentLength = input.value.length;
        countElement.textContent = currentLength;

        // Update styling based on character count
        const parent = countElement.parentElement;
        parent.classList.remove('warning', 'error');

        if (currentLength > maxLength * 0.8) {
            parent.classList.add('warning');
        }
        if (currentLength >= maxLength) {
            parent.classList.add('error');
        }
    }

    validateField(field) {
        const formGroup = field.closest('.form-group');
        const existingError = formGroup.querySelector('.error-message');

        // Remove existing error
        if (existingError) {
            existingError.remove();
        }
        formGroup.classList.remove('error');

        // Validate playlist name
        if (field === this.nameInput) {
            const value = field.value.trim();

            if (!value) {
                this.showFieldError(formGroup, 'Playlist name is required');
                return false;
            }

            if (value.length < 2) {
                this.showFieldError(formGroup, 'Playlist name must be at least 2 characters');
                return false;
            }

            if (value.length > 50) {
                this.showFieldError(formGroup, 'Playlist name cannot exceed 50 characters');
                return false;
            }

            // Check for inappropriate content (basic check)
            const inappropriateWords = ['test', 'spam']; // Add more as needed
            if (inappropriateWords.some(word => value.toLowerCase().includes(word))) {
                this.showFieldError(formGroup, 'Please choose a more appropriate name');
                return false;
            }
        }

        return true;
    }

    showFieldError(formGroup, message) {
        formGroup.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        formGroup.appendChild(errorDiv);
    }

    validateForm() {
        let isValid = true;

        // Validate required fields
        if (!this.validateField(this.nameInput)) {
            isValid = false;
        }

        // Check if name is unique (simulate API check)
        const playlistName = this.nameInput.value.trim();
        if (this.isPlaylistNameTaken(playlistName)) {
            const formGroup = this.nameInput.closest('.form-group');
            this.showFieldError(formGroup, 'A playlist with this name already exists');
            isValid = false;
        }

        return isValid;
    }

    isPlaylistNameTaken(name) {
        // Simulate checking against existing playlists
        const existingNames = ['My Favorites', 'Chill Vibes', 'Workout Mix', 'Road Trip'];
        return existingNames.some(existing =>
            existing.toLowerCase() === name.toLowerCase()
        );
    }

    handleCoverUpload(event) {
        const file = event.target.files[0];

        if (!file) return;

        // Validate file
        if (!this.validateCoverFile(file)) {
            return;
        }

        this.coverFile = file;
        this.displayCoverPreview(file);
        this.announceAction('Cover image uploaded successfully');
    }

    validateCoverFile(file) {
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            this.showError('Please upload a JPG, PNG, or WebP image');
            return false;
        }

        // Check file size (5MB limit)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            this.showError('Image file size must be less than 5MB');
            return false;
        }

        return true;
    }

    displayCoverPreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.coverPreview.innerHTML = `
                <img src="${e.target.result}" alt="Playlist cover preview">
                <div class="cover-overlay">
                    <button type="button" class="remove-cover-btn" onclick="playlistManager.removeCover()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    }

    removeCover() {
        this.coverFile = null;
        this.coverUpload.value = '';
        this.coverPreview.innerHTML = `
            <div class="default-cover">
                <i class="fas fa-music"></i>
                <span>No cover selected</span>
            </div>
        `;
        this.announceAction('Cover image removed');
    }

    generateCover() {
        // Simulate generating a cover based on playlist details
        const playlistName = this.nameInput.value.trim();
        const genre = this.genreSelect.value;
        const mood = this.moodSelect.value;

        if (!playlistName) {
            this.showError('Please enter a playlist name first');
            return;
        }

        // Show loading state
        this.generateCoverBtn.classList.add('loading');
        this.generateCoverBtn.disabled = true;

        // Simulate API call delay
        setTimeout(() => {
            this.createGeneratedCover(playlistName, genre, mood);
            this.generateCoverBtn.classList.remove('loading');
            this.generateCoverBtn.disabled = false;
            this.announceAction('Generated cover created successfully');
        }, 2000);
    }

    createGeneratedCover(name, genre, mood) {
        // Create a simple generated cover using canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 300;
        canvas.height = 300;

        // Background gradient based on genre/mood
        const gradients = {
            pop: ['#FF006E', '#00E0FF'],
            rock: ['#FF4646', '#6F00FF'],
            electronic: ['#00E0FF', '#A7FF4A'],
            jazz: ['#6F00FF', '#FF006E'],
            default: ['#00E0FF', '#FF006E']
        };

        const colors = gradients[genre] || gradients.default;
        const gradient = ctx.createLinearGradient(0, 0, 300, 300);
        gradient.addColorStop(0, colors[0]);
        gradient.addColorStop(1, colors[1]);

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 300, 300);

        // Add playlist name
        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Wrap text if too long
        const maxWidth = 250;
        const words = name.split(' ');
        let line = '';
        let y = 150;

        for (let n = 0; n < words.length; n++) {
            const testLine = line + words[n] + ' ';
            const metrics = ctx.measureText(testLine);
            const testWidth = metrics.width;

            if (testWidth > maxWidth && n > 0) {
                ctx.fillText(line, 150, y);
                line = words[n] + ' ';
                y += 30;
            } else {
                line = testLine;
            }
        }
        ctx.fillText(line, 150, y);

        // Convert to blob and display
        canvas.toBlob((blob) => {
            this.coverFile = blob;
            const url = URL.createObjectURL(blob);
            this.coverPreview.innerHTML = `
                <img src="${url}" alt="Generated playlist cover">
                <div class="cover-overlay">
                    <button type="button" class="remove-cover-btn" onclick="playlistManager.removeCover()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }, 'image/png');
    }

    handleSubmit() {
        if (this.isSubmitting) return;

        // Validate form
        if (!this.validateForm()) {
            this.announceAction('Please fix the errors before submitting');
            return;
        }

        this.isSubmitting = true;
        this.setLoadingState(true);

        // Simulate API call
        setTimeout(() => {
            this.submitPlaylist();
        }, 1500);
    }

    submitPlaylist() {
        try {
            // Collect form data
            const playlistData = {
                name: this.nameInput.value.trim(),
                description: this.descInput.value.trim(),
                privacy: this.privacySelect.value,
                genre: this.genreSelect.value,
                mood: this.moodSelect.value,
                cover: this.coverFile,
                createdAt: new Date().toISOString()
            };

            // Simulate successful creation
            this.showSuccess(`Playlist "${playlistData.name}" created successfully!`);
            this.resetForm();

            // Simulate redirect after success
            setTimeout(() => {
                if (confirm('Playlist created! Would you like to go to your library to see it?')) {
                    window.location.href = 'library.html';
                }
            }, 2000);

        } catch (error) {
            this.showError('Failed to create playlist. Please try again.');
        } finally {
            this.isSubmitting = false;
            this.setLoadingState(false);
        }
    }

    handleCancel() {
        if (this.hasUnsavedChanges()) {
            if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
                this.resetForm();
                window.history.back();
            }
        } else {
            window.history.back();
        }
    }

    hasUnsavedChanges() {
        return this.nameInput.value.trim() !== '' ||
               this.descInput.value.trim() !== '' ||
               this.coverFile !== null ||
               this.genreSelect.value !== '' ||
               this.moodSelect.value !== '';
    }

    resetForm() {
        this.form.reset();
        this.removeCover();
        this.hideMessages();
        this.updateCharacterCount(this.nameInput, this.nameCount, 50);
        this.updateCharacterCount(this.descInput, this.descCount, 200);

        // Remove any error states
        document.querySelectorAll('.form-group.error').forEach(group => {
            group.classList.remove('error');
            const errorMsg = group.querySelector('.error-message');
            if (errorMsg) errorMsg.remove();
        });
    }

    setLoadingState(loading) {
        if (loading) {
            this.createBtn.classList.add('loading');
            this.createBtn.disabled = true;
            this.cancelBtn.disabled = true;
        } else {
            this.createBtn.classList.remove('loading');
            this.createBtn.disabled = false;
            this.cancelBtn.disabled = false;
        }
    }

    showSuccess(message) {
        this.hideMessages();
        this.successMessage.querySelector('span').textContent = message;
        this.successMessage.classList.remove('hidden');
        this.announceAction(message);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.successMessage.classList.add('hidden');
        }, 5000);
    }

    showError(message) {
        this.hideMessages();
        this.errorText.textContent = message;
        this.errorMessage.classList.remove('hidden');
        this.announceAction(message);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.errorMessage.classList.add('hidden');
        }, 5000);
    }

    hideMessages() {
        this.successMessage.classList.add('hidden');
        this.errorMessage.classList.add('hidden');
    }

    announceAction(message) {
        this.liveRegion.textContent = message;

        // Clear the message after a short delay
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
    }

    // Method to pre-fill form (for editing existing playlists)
    loadPlaylistData(playlistData) {
        this.nameInput.value = playlistData.name || '';
        this.descInput.value = playlistData.description || '';
        this.privacySelect.value = playlistData.privacy || 'public';
        this.genreSelect.value = playlistData.genre || '';
        this.moodSelect.value = playlistData.mood || '';

        if (playlistData.coverUrl) {
            this.coverPreview.innerHTML = `
                <img src="${playlistData.coverUrl}" alt="Playlist cover">
                <div class="cover-overlay">
                    <button type="button" class="remove-cover-btn" onclick="playlistManager.removeCover()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        this.updateCharacterCount(this.nameInput, this.nameCount, 50);
        this.updateCharacterCount(this.descInput, this.descCount, 200);
    }
}

// Initialize the playlist manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.playlistManager = new CreatePlaylistManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CreatePlaylistManager;
}