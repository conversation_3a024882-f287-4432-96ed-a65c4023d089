// Subscribe Page Logic
// Handles plan selection, saves to sessionStorage, and redirects to payment.html

document.addEventListener('DOMContentLoaded', function() {
    // Plan selection logic
    const planButtons = document.querySelectorAll('.plan-card .button');
    planButtons.forEach(button => {
        button.addEventListener('click', function() {
            const planType = button.getAttribute('data-plan');
            let plan = {};
            if (planType === 'free') {
                plan = {
                    name: 'Free Account',
                    price: 0.00,
                    interval: 'month',
                    features: [
                        'Enjoy Banshee with occasional ads',
                        'Basic audio quality (128kbps)',
                        'Limited skips (6 per hour)',
                        'Mobile app access'
                    ]
                };
            } else if (planType === 'premium') {
                plan = {
                    name: 'Premium',
                    price: 2.99,
                    interval: 'month',
                    features: [
                        'Ad-free listening experience',
                        'High-quality audio (320kbps)',
                        'Unlimited skips',
                        'Offline mode',
                        'Cross-platform sync',
                        'Exclusive content access'
                    ]
                };
            } else if (planType === 'artist') {
                plan = {
                    name: 'Artist Account',
                    price: 4.99,
                    interval: 'month',
                    features: [
                        'All Premium features included',
                        'Upload unlimited tracks',
                        'Advanced analytics dashboard',
                        'Promotional tools',
                        'Direct fan engagement',
                        'Custom artist profile'
                    ]
                };
            }
            // Save selected plan to sessionStorage
            sessionStorage.setItem('selectedPlan', JSON.stringify(plan));
            // Show loading state
            showLoader();
            button.textContent = 'Processing...';
            button.disabled = true;
            // Simulate processing delay
            setTimeout(() => {
                hideLoader();
                button.textContent = planType === 'free' ? 'Get Started' : planType === 'premium' ? 'Subscribe Now' : 'Start Creating';
                button.disabled = false;
                showToast(`${plan.name} selected successfully!`, 'success');
                setTimeout(() => {
                    if (planType === 'free') {
                        window.location.href = 'index.html?welcome=true';
                    } else {
                        window.location.href = 'payment.html';
                    }
                }, 2000);
            }, 1500);
        });
    });
    // Animate plan cards on scroll
    setupPlanCardAnimations();
});

// Loader functions
function showLoader() {
    const loader = document.getElementById('api-loader');
    if (loader) loader.classList.add('show');
}
function hideLoader() {
    const loader = document.getElementById('api-loader');
    if (loader) loader.classList.remove('show');
}

// Toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '600',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        minWidth: '300px',
        background: type === 'success'
            ? 'linear-gradient(135deg, #4CAF50, #45a049)'
            : 'linear-gradient(135deg, #00E0FF, #FF006E)',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
    });
    document.body.appendChild(toast);
    setTimeout(() => toast.style.transform = 'translateX(0)', 100);
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) document.body.removeChild(toast);
        }, 300);
    }, 4000);
}

// Animate plan cards on scroll
function setupPlanCardAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });
    document.querySelectorAll('.plan-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `all 0.6s ease ${index * 0.2}s`;
        observer.observe(card);
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
}
