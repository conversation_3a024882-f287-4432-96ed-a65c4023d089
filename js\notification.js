// Notification Management System
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.currentFilter = 'all';
        this.init();
    }

    init() {
        this.loadSampleNotifications();
        this.bindEvents();
        this.renderNotifications();
    }

    // Sample notifications data
    loadSampleNotifications() {
        this.notifications = [
            {
                id: 1,
                type: 'music',
                title: 'New Release Alert',
                message: 'Your favorite artist "Cosmic Waves" just released a new album "Stellar Dreams"',
                time: '2 minutes ago',
                unread: true,
                icon: 'fas fa-music'
            },
            {
                id: 2,
                type: 'social',
                title: 'Playlist Shared',
                message: '<PERSON> shared their "Chill Vibes" playlist with you',
                time: '15 minutes ago',
                unread: true,
                icon: 'fas fa-share'
            },
            {
                id: 3,
                type: 'music',
                title: 'Weekly Discovery',
                message: 'Your personalized "Discover Weekly" playlist is ready with 30 new tracks',
                time: '1 hour ago',
                unread: false,
                icon: 'fas fa-compass'
            },
            {
                id: 4,
                type: 'system',
                title: 'Premium Subscription',
                message: 'Your premium subscription will renew in 3 days',
                time: '2 hours ago',
                unread: false,
                icon: 'fas fa-crown'
            },
            {
                id: 5,
                type: 'social',
                title: 'New Follower',
                message: 'MusicLover42 started following your playlists',
                time: '3 hours ago',
                unread: false,
                icon: 'fas fa-user-plus'
            },
            {
                id: 6,
                type: 'music',
                title: 'Concert Alert',
                message: 'Tickets are now available for "Electric Dreams" concert in your city',
                time: '5 hours ago',
                unread: false,
                icon: 'fas fa-ticket-alt'
            },
            {
                id: 7,
                type: 'system',
                title: 'App Update',
                message: 'BansheeBlast v2.1 is available with new features and improvements',
                time: '1 day ago',
                unread: false,
                icon: 'fas fa-download'
            },
            {
                id: 8,
                type: 'music',
                title: 'Playlist Milestone',
                message: 'Your "Road Trip Hits" playlist reached 100 likes!',
                time: '2 days ago',
                unread: false,
                icon: 'fas fa-heart'
            }
        ];
    }

    bindEvents() {
        // Filter tabs
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Action buttons
        document.getElementById('markAllReadBtn').addEventListener('click', () => {
            this.markAllAsRead();
        });

        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllNotifications();
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.clearSelection();
            }
        });
    }

    setFilter(filter) {
        this.currentFilter = filter;

        // Update active tab
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        this.renderNotifications();
        this.announceFilterChange(filter);
    }

    getFilteredNotifications() {
        if (this.currentFilter === 'all') {
            return this.notifications;
        }
        return this.notifications.filter(notification => notification.type === this.currentFilter);
    }

    renderNotifications() {
        const container = document.getElementById('notificationsList');
        const emptyState = document.getElementById('emptyState');
        const filteredNotifications = this.getFilteredNotifications();

        if (filteredNotifications.length === 0) {
            container.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');

        container.innerHTML = filteredNotifications.map(notification =>
            this.createNotificationHTML(notification)
        ).join('');

        // Add event listeners to notification items
        this.bindNotificationEvents();
    }

    createNotificationHTML(notification) {
        const unreadClass = notification.unread ? 'unread' : '';

        return `
            <div class="notification-item ${unreadClass}" data-id="${notification.id}" tabindex="0">
                <div class="notification-header">
                    <div style="display: flex; align-items: flex-start;">
                        <div class="notification-icon ${notification.type}">
                            <i class="${notification.icon}"></i>
                        </div>
                        <div class="notification-content">
                            <h3 class="notification-title">${notification.title}</h3>
                            <p class="notification-message">${notification.message}</p>
                        </div>
                    </div>
                </div>
                <div class="notification-meta">
                    <span class="notification-time">${notification.time}</span>
                    <div class="notification-actions">
                        ${notification.unread ? '<button type="button" class="notification-action mark-read" data-id="' + notification.id + '">Mark as Read</button>' : ''}
                        <button type="button" class="notification-action delete" data-id="${notification.id}">Delete</button>
                    </div>
                </div>
            </div>
        `;
    }

    getIconClass(type) {
        const iconMap = {
            music: 'music',
            social: 'social',
            system: 'system'
        };
        return iconMap[type] || 'system';
    }

    bindNotificationEvents() {
        // Click to mark as read
        document.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('notification-action')) {
                    const id = parseInt(item.dataset.id);
                    this.markAsRead(id);
                }
            });

            // Keyboard support
            item.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const id = parseInt(item.dataset.id);
                    this.markAsRead(id);
                }
            });
        });

        // Action buttons
        document.querySelectorAll('.notification-action.mark-read').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.markAsRead(id);
            });
        });

        document.querySelectorAll('.notification-action.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.deleteNotification(id);
            });
        });
    }

    markAsRead(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification && notification.unread) {
            notification.unread = false;
            this.renderNotifications();
            this.announceAction(`Notification "${notification.title}" marked as read`);
        }
    }

    deleteNotification(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            // Add slide-out animation
            const element = document.querySelector(`[data-id="${id}"]`);
            if (element) {
                element.classList.add('slide-out');
                setTimeout(() => {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                    this.renderNotifications();
                    this.announceAction(`Notification "${notification.title}" deleted`);
                }, 300);
            }
        }
    }

    markAllAsRead() {
        const unreadCount = this.notifications.filter(n => n.unread).length;
        if (unreadCount === 0) {
            this.announceAction('No unread notifications to mark');
            return;
        }

        this.notifications.forEach(notification => {
            notification.unread = false;
        });

        this.renderNotifications();
        this.announceAction(`${unreadCount} notifications marked as read`);
    }

    clearAllNotifications() {
        if (this.notifications.length === 0) {
            this.announceAction('No notifications to clear');
            return;
        }

        const count = this.notifications.length;

        // Show confirmation dialog
        if (confirm(`Are you sure you want to delete all ${count} notifications? This action cannot be undone.`)) {
            this.notifications = [];
            this.renderNotifications();
            this.announceAction(`All ${count} notifications cleared`);
        }
    }

    clearSelection() {
        // Remove focus from any focused notification
        document.activeElement.blur();
    }

    announceFilterChange(filter) {
        const filterNames = {
            all: 'All notifications',
            music: 'Music notifications',
            social: 'Social notifications',
            system: 'System notifications'
        };

        const count = this.getFilteredNotifications().length;
        const message = `Showing ${filterNames[filter]}. ${count} notification${count !== 1 ? 's' : ''} found.`;
        this.announceAction(message);
    }

    announceAction(message) {
        const liveRegion = document.getElementById('aria-live-region');
        liveRegion.textContent = message;

        // Clear the message after a short delay to allow for new announcements
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }

    // Method to add new notifications (for future use)
    addNotification(notification) {
        const newId = Math.max(...this.notifications.map(n => n.id), 0) + 1;
        const newNotification = {
            id: newId,
            unread: true,
            time: 'Just now',
            ...notification
        };

        this.notifications.unshift(newNotification);
        this.renderNotifications();

        // Add slide-in animation to new notification
        setTimeout(() => {
            const element = document.querySelector(`[data-id="${newId}"]`);
            if (element) {
                element.classList.add('slide-in');
            }
        }, 100);
    }

    // Get unread count (for future use with badge)
    getUnreadCount() {
        return this.notifications.filter(n => n.unread).length;
    }
}

// Initialize the notification manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}