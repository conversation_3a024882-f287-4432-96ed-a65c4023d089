// Shared utilities for Banshee Music App
// Consolidates common functionality used across multiple pages

class BansheeUtils {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.liveRegion = this.createLiveRegion();
    }

    // ===== ACCESSIBILITY UTILITIES =====
    createLiveRegion() {
        let liveRegion = document.getElementById('aria-live-region');
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'aria-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.position = 'absolute';
            liveRegion.style.left = '-10000px';
            liveRegion.style.width = '1px';
            liveRegion.style.height = '1px';
            liveRegion.style.overflow = 'hidden';
            document.body.appendChild(liveRegion);
        }
        return liveRegion;
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
        }
    }

    // ===== INTERSECTION OBSERVER UTILITIES =====
    createFadeInObserver(options = {}) {
        const defaultOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px',
            staggerDelay: 100
        };
        const config = { ...defaultOptions, ...options };

        return new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * config.staggerDelay);
                }
            });
        }, {
            threshold: config.threshold,
            rootMargin: config.rootMargin
        });
    }

    observeElements(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const observer = this.createFadeInObserver(options);

        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(element);
        });

        return observer;
    }

    // ===== API UTILITIES =====
    async fetchWithFallback(endpoint, fallbackData = null) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`);
            if (response.ok) {
                return await response.json();
            }
            throw new Error(`HTTP ${response.status}`);
        } catch (error) {
            console.warn(`⚠️ API call failed for ${endpoint}:`, error.message);
            if (fallbackData) {
                console.log(`📦 Using fallback data for ${endpoint}`);
                return fallbackData;
            }
            throw error;
        }
    }

    async fetchMultiple(endpoints) {
        const promises = endpoints.map(endpoint => 
            this.fetchWithFallback(endpoint.url, endpoint.fallback)
        );
        
        try {
            return await Promise.allSettled(promises);
        } catch (error) {
            console.error('❌ Multiple fetch failed:', error);
            return [];
        }
    }

    // ===== ANIMATION UTILITIES =====
    animateNumber(element, targetValue, duration = 1000) {
        if (!element) return;

        const startValue = parseInt(element.textContent) || 0;
        const difference = targetValue - startValue;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = Math.round(startValue + (difference * easeOut));
            
            element.textContent = this.formatNumber(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    // ===== DOM UTILITIES =====
    createElement(tag, className = '', content = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (content) element.innerHTML = content;
        return element;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // ===== CARD RENDERING UTILITIES =====
    renderTrackCard(track, type = 'default') {
        const playButton = `
            <div class="play-overlay">
                <button type="button" class="play-button" aria-label="Play ${track.title}">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        `;

        return `
            <div class="carousel-card" data-track-id="${track.id}">
                <div class="card">
                    <div class="img-container">
                        <img src="${track.image || track.album?.cover_medium || 'imgs/album-01.png'}" 
                             alt="${track.title}" loading="lazy" />
                        ${playButton}
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${track.title}</h3>
                            <p>Artist: ${track.artist?.name || track.artist} • ${track.duration ? this.formatDuration(track.duration) : 'Unknown'}</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
            </div>
        `;
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // ===== EVENT HANDLING UTILITIES =====
    bindPlayButtons(container, callback) {
        if (!container) return;
        
        container.querySelectorAll('.play-button, .play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('[data-track-id], .card, .result-card');
                const trackId = card?.dataset.trackId || card?.dataset.id;
                if (callback && trackId) {
                    callback(trackId, card);
                }
            });
        });
    }

    // ===== LOADING STATES =====
    showLoading(container, message = 'Loading...') {
        if (!container) return;
        container.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    }

    hideLoading(container) {
        if (!container) return;
        const loadingState = container.querySelector('.loading-state');
        if (loadingState) {
            loadingState.remove();
        }
    }

    // ===== ERROR HANDLING =====
    showError(container, message = 'Something went wrong') {
        if (!container) return;
        container.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button type="button" class="retry-btn">Try Again</button>
            </div>
        `;
    }
}

// Create global instance
window.bansheeUtils = new BansheeUtils();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BansheeUtils;
}
