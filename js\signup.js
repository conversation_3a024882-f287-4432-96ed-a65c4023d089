document.addEventListener('DOMContentLoaded', () => {
    // Initialize the signup form
    const signupForm = document.getElementById('signupForm');
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }
    // Redirect if already logged in
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const user = localStorage.getItem('user') || sessionStorage.getItem('user');
    if (token && user) {
        window.location.href = 'index.html';
    }

    // Password strength bar logic
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', updatePasswordStrengthBar);
    }

    // Live username/email availability check
    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    if (usernameInput) {
        usernameInput.addEventListener('input', function() {
            const users = JSON.parse(localStorage.getItem('banshee_users') || '[]');
            const taken = users.some(u => u.username === usernameInput.value.trim());
            const msg = usernameInput.parentElement.querySelector('.validation-message');
            if (usernameInput.value.length < 3) {
                msg.textContent = '';
                msg.className = 'validation-message';
            } else if (taken) {
                msg.textContent = 'Username is already taken.';
                msg.className = 'validation-message error';
            } else {
                msg.textContent = 'Username is available!';
                msg.className = 'validation-message success';
            }
        });
    }
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const users = JSON.parse(localStorage.getItem('banshee_users') || '[]');
            const taken = users.some(u => u.email === emailInput.value.trim());
            const msg = emailInput.parentElement.querySelector('.validation-message');
            const valid = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i.test(emailInput.value);
            if (!valid && emailInput.value.length > 0) {
                msg.textContent = 'Invalid email format.';
                msg.className = 'validation-message error';
            } else if (taken) {
                msg.textContent = 'Email is already registered.';
                msg.className = 'validation-message error';
            } else if (valid) {
                msg.textContent = 'Email looks good!';
                msg.className = 'validation-message success';
            } else {
                msg.textContent = '';
                msg.className = 'validation-message';
            }
        });
    }
    // Toggle password visibility
    document.querySelectorAll('.password-toggle').forEach(icon => {
        icon.addEventListener('click', function() {
            const input = this.previousElementSibling;
            if (input.type === 'password') {
                input.type = 'text';
                this.classList.add('active');
            } else {
                input.type = 'password';
                this.classList.remove('active');
            }
        });
    });
    // Live password confirmation feedback
    const confirmInput = document.getElementById('confirm-password');
    if (confirmInput) {
        confirmInput.addEventListener('input', function() {
            const msg = confirmInput.parentElement.querySelector('.validation-message');
            const password = document.getElementById('password').value;
            if (confirmInput.value.length === 0) {
                msg.textContent = '';
                msg.className = 'validation-message';
            } else if (confirmInput.value !== password) {
                msg.textContent = 'Passwords do not match.';
                msg.className = 'validation-message error';
            } else {
                msg.textContent = 'Passwords match!';
                msg.className = 'validation-message success';
            }
        });
    }
    // Profile picture preview
    const profilePictureInput = document.getElementById('profile-picture');
    const profilePictureImg = document.getElementById('profile-picture-img');
    if (profilePictureInput && profilePictureImg) {
        profilePictureInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    profilePictureImg.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Password rules checkmarks
    const passwordRules = document.getElementById('password-rules');
    if (passwordInput && passwordRules) {
        passwordInput.addEventListener('input', function() {
            const val = passwordInput.value;
            passwordRules.querySelector('[data-rule="length"]').className = val.length >= 8 ? 'valid' : 'invalid';
            passwordRules.querySelector('[data-rule="uppercase"]').className = /[A-Z]/.test(val) ? 'valid' : 'invalid';
            passwordRules.querySelector('[data-rule="lowercase"]').className = /[a-z]/.test(val) ? 'valid' : 'invalid';
            passwordRules.querySelector('[data-rule="number"]').className = /\d/.test(val) ? 'valid' : 'invalid';
            passwordRules.querySelector('[data-rule="special"]').className = /[@$!%*?&]/.test(val) ? 'valid' : 'invalid';
        });
    }

    // Show/hide password for confirm
    document.querySelectorAll('.input-group .password-toggle').forEach(icon => {
        icon.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                icon.click();
            }
        });
        icon.setAttribute('tabindex', '0');
    });

    // Terms/Privacy modals
    const termsModal = document.getElementById('terms-modal');
    const privacyModal = document.getElementById('privacy-modal');
    const verifyModal = document.getElementById('verify-modal');
    document.getElementById('terms-link').addEventListener('click', e => {
        e.preventDefault();
        termsModal.style.display = 'flex';
    });
    document.getElementById('privacy-link').addEventListener('click', e => {
        e.preventDefault();
        privacyModal.style.display = 'flex';
    });
    document.getElementById('close-terms').onclick = () => termsModal.style.display = 'none';
    document.getElementById('close-privacy').onclick = () => privacyModal.style.display = 'none';
    window.onclick = function(event) {
        if (event.target === termsModal) termsModal.style.display = 'none';
        if (event.target === privacyModal) privacyModal.style.display = 'none';
        if (event.target === verifyModal) verifyModal.style.display = 'none';
    };

    // Social signup tooltips (keyboard accessible)
    document.querySelectorAll('.social-button[data-tooltip]').forEach(btn => {
        btn.addEventListener('focus', function() {
            btn.classList.add('show-tooltip');
        });
        btn.addEventListener('blur', function() {
            btn.classList.remove('show-tooltip');
        });
    });
});

let signupAttempts = 0;
const MAX_ATTEMPTS = 5;
let lockoutTimeout = null;

function handleSignup(event) {
    event.preventDefault();
    if (lockoutTimeout) {
        showErrorSummary(['Too many failed attempts. Please wait and try again.']);
        return;
    }
    const formData = new FormData(event.target);
    const userData = Object.fromEntries(formData);
    const errors = [];
    // Validate all fields for error summary
    if (!userData.username || userData.username.length < 3) errors.push('Username must be at least 3 characters.');
    if (!userData.email || !/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i.test(userData.email)) errors.push('Enter a valid email address.');
    if (!userData.password || !isStrongPassword(userData.password)) errors.push('Password does not meet requirements.');
    if (userData.password !== userData['confirm-password']) errors.push('Passwords do not match.');
    if (!event.target.querySelector('.terms-group input[type="checkbox"]').checked) errors.push('You must agree to the Terms of Service and Privacy Policy.');
    if (errors.length > 0) {
        showErrorSummary(errors);
        shakeForm();
        signupAttempts++;
        if (signupAttempts >= MAX_ATTEMPTS) {
            lockoutTimeout = setTimeout(() => {
                signupAttempts = 0;
                lockoutTimeout = null;
                hideErrorSummary();
            }, 15000);
        }
        return;
    }
    hideErrorSummary();
    // Check terms checkbox
    if (!event.target.querySelector('input[type="checkbox"]').checked) {
        showMessage('You must agree to the Terms of Service and Privacy Policy.', true);
        return;
    }

    // Password confirmation fix
    if (userData.password !== userData['confirm-password']) {
        showMessage('Passwords do not match', true);
        return;
    }

    // Password strength check (basic)
    if (!isStrongPassword(userData.password)) {
        showMessage('Password must be at least 8 characters, include uppercase, lowercase, number, and special character.', true);
        return;
    }

    // Check if username or email already exists
    const users = JSON.parse(localStorage.getItem('banshee_users') || '[]');
    if (users.some(u => u.username === userData.username)) {
        showErrorSummary(['Username already exists.']);
        shakeForm();
        return;
    }
    if (users.some(u => u.email === userData.email)) {
        showErrorSummary(['Email already registered.']);
        shakeForm();
        return;
    }
    showLoader();
    setTimeout(() => {
        // Add new user
        const newUser = {
            id: Date.now(),
            username: userData.username,
            password: userData.password,
            name: userData.username,
            email: userData.email,
            role: 'user',
            profilePicture: userData['profile-picture'] || ''
        };
        users.push(newUser);
        localStorage.setItem('banshee_users', JSON.stringify(users));
        hideLoader();
        // Show email verification modal
        document.getElementById('verify-modal').style.display = 'flex';
    }, 1200);
}

function isStrongPassword(password) {
    // At least 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password);
}

function showMessage(message, isError = false) {
    const messageElement = document.getElementById('message');
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.className = `message ${isError ? 'error' : 'success'}`;
        messageElement.style.display = 'block';
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 3000);
    } else {
        if (isError) {
            alert('Error: ' + message);
        } else {
            alert(message);
        }
    }
}

function showLoader() {
    const loader = document.getElementById('loader');
    if (loader) loader.style.display = 'flex';
}
function hideLoader() {
    const loader = document.getElementById('loader');
    if (loader) loader.style.display = 'none';
}

function updatePasswordStrengthBar() {
    const password = document.getElementById('password').value;
    const bars = document.querySelectorAll('.strength-bar');
    let score = 0;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[@$!%*?&]/.test(password)) score++;

    bars.forEach(bar => bar.classList.remove('active', 'neon'));
    if (score <= 2) {
        bars[0].classList.add('active');
    } else if (score === 3 || score === 4) {
        bars[0].classList.add('active');
        bars[1].classList.add('active');
    } else if (score === 5) {
        bars[0].classList.add('active');
        bars[1].classList.add('active');
        bars[2].classList.add('active', 'neon'); // Neon for strong
    }
}

// Error summary and shaking form on error
function showErrorSummary(errors) {
    const summary = document.getElementById('error-summary');
    if (summary) {
        summary.innerHTML = '<ul>' + errors.map(e => `<li>${e}</li>`).join('') + '</ul>';
        summary.style.display = 'block';
        summary.classList.add('shake');
        setTimeout(() => summary.classList.remove('shake'), 400);
    }
}
function hideErrorSummary() {
    const summary = document.getElementById('error-summary');
    if (summary) summary.style.display = 'none';
}
function shakeForm() {
    const form = document.getElementById('signupForm');
    if (form) {
        form.classList.add('shake');
        setTimeout(() => form.classList.remove('shake'), 400);
    }
}

// Resend verification (demo)
document.addEventListener('DOMContentLoaded', () => {
    const resendBtn = document.getElementById('resend-verification');
    if (resendBtn) {
        resendBtn.onclick = function() {
            resendBtn.textContent = 'Resent!';
            setTimeout(() => resendBtn.textContent = 'Resend Email', 2000);
        };
    }
});
