// Simple Express server for bansheeblast Library API
const express = require('express');
const app = express();
const cors = require('cors');  // Import the cors middleware
const PORT = process.env.PORT || 3001;

app.use(cors());  // Enable CORS for all routes
app.use(express.static('.')); // Serve static files from current directory

app.use(express.json());

// Sample track data for demo - Enhanced with popular artists for better search results
const sampleTracks = {
  101: { id: 101, title: "Blinding Lights", artist: "The Weeknd", album: "After Hours", duration: "3:20", cover: "imgs/album-01.png", genre: "Pop", year: 2020, plays: 2500 },
  102: { id: 102, title: "Save Your Tears", artist: "The Weeknd", album: "After Hours", duration: "3:35", cover: "imgs/album-02.png", genre: "Pop", year: 2020, plays: 1890 },
  103: { id: 103, title: "Can't Feel My Face", artist: "The Weeknd", album: "Beauty Behind the Madness", duration: "3:35", cover: "imgs/album-03-B.png", genre: "Pop", year: 2015, plays: 2100 },
  104: { id: 104, title: "Starboy", artist: "The Weeknd", album: "Starboy", duration: "3:50", cover: "imgs/album-04-B.png", genre: "Pop", year: 2016, plays: 1980 },
  105: { id: 105, title: "Anti-Hero", artist: "Taylor Swift", album: "Midnights", duration: "3:20", cover: "imgs/album-01.png", genre: "Pop", year: 2022, plays: 2200 },
  106: { id: 106, title: "As It Was", artist: "Harry Styles", album: "Harry's House", duration: "2:47", cover: "imgs/album-02.png", genre: "Pop", year: 2022, plays: 1920 },
  107: { id: 107, title: "Heat Waves", artist: "Glass Animals", album: "Dreamland", duration: "3:58", cover: "imgs/album-03-B.png", genre: "Indie", year: 2020, plays: 1800 },
  108: { id: 108, title: "Stay", artist: "The Kid LAROI & Justin Bieber", album: "F*CK LOVE 3", duration: "2:21", cover: "imgs/album-04-B.png", genre: "Pop", year: 2021, plays: 1720 },
  109: { id: 109, title: "Good 4 U", artist: "Olivia Rodrigo", album: "SOUR", duration: "2:58", cover: "imgs/album-01.png", genre: "Pop", year: 2021, plays: 1650 },
  110: { id: 110, title: "Levitating", artist: "Dua Lipa", album: "Future Nostalgia", duration: "3:23", cover: "imgs/album-02.png", genre: "Pop", year: 2020, plays: 1850 },
  111: { id: 111, title: "Watermelon Sugar", artist: "Harry Styles", album: "Fine Line", duration: "2:54", cover: "imgs/album-03-B.png", genre: "Pop", year: 2020, plays: 1600 },
  112: { id: 112, title: "Drivers License", artist: "Olivia Rodrigo", album: "SOUR", duration: "4:02", cover: "imgs/album-04-B.png", genre: "Pop", year: 2021, plays: 1750 }
};

// Example in-memory data (replace with DB later) - Enhanced with more realistic data
let userLibrary = {
  playlists: [
    {
      id: 1,
      name: 'Favorites',
      tracks: [101, 102, 104, 107],
      description: 'My all-time favorite tracks',
      cover: 'imgs/playlist-01.png',
      created: '2024-01-15',
      lastModified: '2024-06-20'
    },
    {
      id: 2,
      name: 'Chill Vibes',
      tracks: [103, 105, 109],
      description: 'Perfect for relaxing and unwinding',
      cover: 'imgs/playlist-02.png',
      created: '2024-02-10',
      lastModified: '2024-06-18'
    },
    {
      id: 3,
      name: 'Workout Mix',
      tracks: [106, 101, 110, 103],
      description: 'High energy tracks for the gym',
      cover: 'imgs/playlist-03.png',
      created: '2024-03-05',
      lastModified: '2024-06-25'
    },
    {
      id: 4,
      name: 'Night Drive',
      tracks: [107, 106, 104],
      description: 'Perfect soundtrack for late night drives',
      cover: 'imgs/playlist-04.png',
      created: '2024-04-12',
      lastModified: '2024-06-22'
    },
    {
      id: 5,
      name: 'Study Focus',
      tracks: [102, 109, 104],
      description: 'Ambient tracks for concentration',
      cover: 'imgs/playlist-05.png',
      created: '2024-05-08',
      lastModified: '2024-06-15'
    }
  ],
  likedSongs: [101, 104, 105, 107, 109],
  recentlyPlayed: [103, 110, 101, 106, 108, 111],
  topArtists: ['The Weeknd', 'Taylor Swift', 'Harry Styles', 'Olivia Rodrigo'],
  stats: {
    totalPlaylists: 5,
    totalLikedSongs: 5,
    totalListeningTime: '24h 32m',
    topGenre: 'Electronic'
  }
};

// Get all playlists
app.get('/api/library', (req, res) => {
    res.json(userLibrary);
  });


// Get track details
app.get('/api/tracks/:id', (req, res) => {
  const trackId = parseInt(req.params.id);
  const track = sampleTracks[trackId];
  if (track) {
    res.json(track);
  } else {
    res.status(404).json({ error: 'Track not found' });
  }
});

// Get all playlists
app.get('/api/library/playlists', (req, res) => {
  res.json(userLibrary.playlists);
});

// Get liked songs with track details
app.get('/api/library/liked', (req, res) => {
  const likedTracksWithDetails = userLibrary.likedSongs.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(likedTracksWithDetails);
});

// Get recently played tracks
app.get('/api/library/recent', (req, res) => {
  const recentTracksWithDetails = userLibrary.recentlyPlayed.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(recentTracksWithDetails);
});

// Get user stats
app.get('/api/library/stats', (req, res) => {
  res.json(userLibrary.stats);
});

// Get top artists
app.get('/api/library/artists', (req, res) => {
  res.json(userLibrary.topArtists);
});

// ===== ENHANCED SEARCH API ENDPOINTS =====

// Comprehensive search across all content types
app.get('/api/search', (req, res) => {
  const query = req.query.q?.toLowerCase() || '';
  const contentType = req.query.type || 'all'; // all, songs, artists, albums, playlists
  const genre = req.query.genre || '';
  const sort = req.query.sort || 'relevance'; // relevance, popularity, recent, alphabetical
  const limit = parseInt(req.query.limit) || 50;

  if (!query) {
    return res.json({
      query: '',
      total: 0,
      tracks: [],
      artists: [],
      albums: [],
      playlists: [],
      topResult: null
    });
  }

  // Search tracks
  let matchingTracks = Object.values(sampleTracks).filter(track =>
    track.title.toLowerCase().includes(query) ||
    track.artist.toLowerCase().includes(query) ||
    track.album.toLowerCase().includes(query) ||
    track.genre.toLowerCase().includes(query)
  );

  // Search playlists
  let matchingPlaylists = userLibrary.playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(query) ||
    playlist.description.toLowerCase().includes(query)
  );

  // Generate artists from tracks
  let matchingArtists = [...new Set(matchingTracks.map(track => track.artist))]
    .filter(artistName => artistName.toLowerCase().includes(query))
    .map(artistName => {
      const artistTracks = Object.values(sampleTracks).filter(track => track.artist === artistName);
      const totalPlays = artistTracks.reduce((sum, track) => sum + track.plays, 0);
      return {
        id: artistName.replace(/\s+/g, '-').toLowerCase(),
        name: artistName,
        genre: artistTracks[0]?.genre || 'Unknown',
        followers: Math.floor(totalPlays / 10) + 'K',
        image: artistTracks[0]?.cover || 'imgs/album-01.png',
        trackCount: artistTracks.length,
        topTrack: artistTracks.sort((a, b) => b.plays - a.plays)[0]
      };
    });

  // Generate albums from tracks
  let matchingAlbums = [...new Set(matchingTracks.map(track => track.album))]
    .filter(albumName => albumName.toLowerCase().includes(query))
    .map(albumName => {
      const albumTracks = Object.values(sampleTracks).filter(track => track.album === albumName);
      return {
        id: albumName.replace(/\s+/g, '-').toLowerCase(),
        title: albumName,
        artist: albumTracks[0]?.artist || 'Unknown',
        year: albumTracks[0]?.year || 2024,
        tracks: albumTracks.length,
        image: albumTracks[0]?.cover || 'imgs/album-01.png',
        genre: albumTracks[0]?.genre || 'Unknown'
      };
    });

  // Apply genre filter
  if (genre && genre !== 'all') {
    matchingTracks = matchingTracks.filter(track => track.genre.toLowerCase() === genre.toLowerCase());
    matchingArtists = matchingArtists.filter(artist => artist.genre.toLowerCase() === genre.toLowerCase());
    matchingAlbums = matchingAlbums.filter(album => album.genre.toLowerCase() === genre.toLowerCase());
  }

  // Apply sorting
  const sortFunctions = {
    popularity: (a, b) => (b.plays || b.totalPlays || 0) - (a.plays || a.totalPlays || 0),
    recent: (a, b) => (b.year || 0) - (a.year || 0),
    alphabetical: (a, b) => (a.title || a.name || '').localeCompare(b.title || b.name || '')
  };

  if (sortFunctions[sort]) {
    matchingTracks.sort(sortFunctions[sort]);
    matchingArtists.sort(sortFunctions[sort]);
    matchingAlbums.sort(sortFunctions[sort]);
    matchingPlaylists.sort((a, b) => a.name.localeCompare(b.name));
  }

  // Determine top result (most relevant)
  let topResult = null;
  if (matchingTracks.length > 0) {
    topResult = { type: 'track', ...matchingTracks[0] };
  } else if (matchingArtists.length > 0) {
    topResult = { type: 'artist', ...matchingArtists[0] };
  } else if (matchingAlbums.length > 0) {
    topResult = { type: 'album', ...matchingAlbums[0] };
  } else if (matchingPlaylists.length > 0) {
    topResult = { type: 'playlist', ...matchingPlaylists[0] };
  }

  // Apply content type filter and limits
  const results = {
    query,
    total: matchingTracks.length + matchingArtists.length + matchingAlbums.length + matchingPlaylists.length,
    tracks: contentType === 'all' || contentType === 'songs' ? matchingTracks.slice(0, limit) : [],
    artists: contentType === 'all' || contentType === 'artists' ? matchingArtists.slice(0, limit) : [],
    albums: contentType === 'all' || contentType === 'albums' ? matchingAlbums.slice(0, limit) : [],
    playlists: contentType === 'all' || contentType === 'playlists' ? matchingPlaylists.slice(0, limit) : [],
    topResult
  };

  res.json(results);
});

// Legacy library search endpoint (for backward compatibility)
app.get('/api/library/search', (req, res) => {
  const query = req.query.q?.toLowerCase() || '';
  if (!query) {
    return res.json({ tracks: [], playlists: [] });
  }

  // Search tracks
  const matchingTracks = Object.values(sampleTracks).filter(track =>
    track.title.toLowerCase().includes(query) ||
    track.artist.toLowerCase().includes(query) ||
    track.album.toLowerCase().includes(query) ||
    track.genre.toLowerCase().includes(query)
  );

  // Search playlists
  const matchingPlaylists = userLibrary.playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(query) ||
    playlist.description.toLowerCase().includes(query)
  );

  res.json({
    tracks: matchingTracks,
    playlists: matchingPlaylists
  });
});

// Enhanced playlist creation endpoint
app.post('/api/library/playlists', (req, res) => {
  const { name, description = '', privacy = 'private', genre = '', mood = '', cover = null } = req.body;

  // Validation
  if (!name || name.trim().length < 2) {
    return res.status(400).json({
      error: 'Playlist name is required and must be at least 2 characters',
      field: 'name'
    });
  }

  if (name.trim().length > 50) {
    return res.status(400).json({
      error: 'Playlist name cannot exceed 50 characters',
      field: 'name'
    });
  }

  // Check for duplicate names
  const existingPlaylist = userLibrary.playlists.find(p =>
    p.name.toLowerCase() === name.trim().toLowerCase()
  );

  if (existingPlaylist) {
    return res.status(409).json({
      error: 'A playlist with this name already exists',
      field: 'name'
    });
  }

  const newPlaylist = {
    id: Date.now(),
    name: name.trim(),
    tracks: [],
    description: description.trim(),
    privacy,
    genre,
    mood,
    cover: cover || 'imgs/playlist-default.png',
    created: new Date().toISOString().split('T')[0],
    lastModified: new Date().toISOString().split('T')[0],
    createdBy: 'user', // In real app, this would be the authenticated user ID
    likes: 0,
    plays: 0
  };

  userLibrary.playlists.push(newPlaylist);
  userLibrary.stats.totalPlaylists = userLibrary.playlists.length;

  res.status(201).json({
    success: true,
    playlist: newPlaylist,
    message: `Playlist "${newPlaylist.name}" created successfully!`
  });
});

// Check if playlist name is available
app.post('/api/library/playlists/check-name', (req, res) => {
  const { name } = req.body;

  if (!name) {
    return res.status(400).json({ error: 'Name is required' });
  }

  const existingPlaylist = userLibrary.playlists.find(p =>
    p.name.toLowerCase() === name.trim().toLowerCase()
  );

  res.json({
    available: !existingPlaylist,
    message: existingPlaylist ? 'A playlist with this name already exists' : 'Name is available'
  });
});

// Generate playlist cover
app.post('/api/library/playlists/generate-cover', (req, res) => {
  const { name, genre, mood } = req.body;

  if (!name) {
    return res.status(400).json({ error: 'Playlist name is required' });
  }

  // Simulate cover generation based on playlist details
  const coverStyles = {
    'pop': ['#FF6B6B', '#4ECDC4', '#45B7D1'],
    'rock': ['#2C3E50', '#E74C3C', '#F39C12'],
    'electronic': ['#9B59B6', '#3498DB', '#1ABC9C'],
    'hip-hop': ['#34495E', '#E67E22', '#F1C40F'],
    'jazz': ['#8E44AD', '#D35400', '#27AE60'],
    'classical': ['#2C3E50', '#8E44AD', '#16A085'],
    'default': ['#3498DB', '#9B59B6', '#E74C3C']
  };

  const moodEffects = {
    'energetic': 'gradient-radial',
    'chill': 'gradient-linear',
    'romantic': 'gradient-soft',
    'focus': 'gradient-minimal',
    'party': 'gradient-vibrant',
    'default': 'gradient-linear'
  };

  const colors = coverStyles[genre] || coverStyles['default'];
  const effect = moodEffects[mood] || moodEffects['default'];

  // Generate a unique cover URL (in real app, this would generate actual image)
  const coverUrl = `imgs/generated-covers/${Date.now()}-${name.toLowerCase().replace(/\s+/g, '-')}.png`;

  setTimeout(() => {
    res.json({
      success: true,
      coverUrl,
      coverData: {
        colors,
        effect,
        text: name,
        style: `${genre}-${mood}`
      },
      message: 'Cover generated successfully!'
    });
  }, 1500); // Simulate processing time
});

// Delete a playlist
app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const index = userLibrary.playlists.findIndex(p => p.id === id);
  if (index !== -1) {
    userLibrary.playlists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add/Remove a song to liked songs
app.post('/api/library/liked/:songId', (req, res) => {
    const songId = parseInt(req.params.songId);
    if (!userLibrary.likedSongs.includes(songId)) {
      userLibrary.likedSongs.push(songId);
      res.status(201).json({ success: true, liked: true, songId });
    } else {
        removeLikedSong(songId, res);
    }
});

// Remove a song from liked songs
app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  removeLikedSong(songId, res);
});

function removeLikedSong(songId, res) {
    const idx = userLibrary.likedSongs.indexOf(songId);
    if (idx !== -1) {
      userLibrary.likedSongs.splice(idx, 1);
      res.json({ success: true, liked: false, songId });
    } else {
      res.status(404).json({ error: 'Song not found in liked songs' });
    }
}

// Edit a playlist (name, description, etc.)
app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { name, description, cover } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    if (name) playlist.name = name;
    if (description !== undefined) playlist.description = description;
    if (cover) playlist.cover = cover;
    playlist.lastModified = new Date().toISOString().split('T')[0];
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Get a single playlist by ID
app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add a track to a playlist
app.post('/api/library/playlists/:id/tracks', (req, res) => {
  const id = parseInt(req.params.id);
  const { trackId } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist && trackId) {
    if (!playlist.tracks.includes(trackId)) {
      playlist.tracks.push(trackId);
    }
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found or invalid trackId' });
  }
});

// Remove a track from a playlist
app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const id = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    const idx = playlist.tracks.indexOf(trackId);
    if (idx !== -1) {
      playlist.tracks.splice(idx, 1);
      res.json({ success: true, playlist });
    } else {
      res.status(404).json({ error: 'Track not found in playlist' });
    }
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// ===== HOME PAGE API ENDPOINTS =====

// Get featured content for home page
app.get('/api/home/<USER>', (req, res) => {
  // Select top tracks based on plays
  const featuredTracks = Object.values(sampleTracks)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 6);

  // Select featured playlists
  const featuredPlaylists = userLibrary.playlists.slice(0, 3);

  res.json({
    tracks: featuredTracks,
    playlists: featuredPlaylists
  });
});

// Get trending content
app.get('/api/home/<USER>', (req, res) => {
  // Simulate trending based on recent plays and popularity
  const trendingTracks = Object.values(sampleTracks)
    .filter(track => track.year >= 2020) // Recent tracks
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 8);

  res.json(trendingTracks);
});

// Get new releases
app.get('/api/home/<USER>', (req, res) => {
  // Get newest tracks (2022 and later)
  const newReleases = Object.values(sampleTracks)
    .filter(track => track.year >= 2022)
    .sort((a, b) => b.year - a.year)
    .slice(0, 6);

  res.json(newReleases);
});

// Get personalized recommendations based on user's library
app.get('/api/home/<USER>', (req, res) => {
  // Get user's liked songs and playlists to generate recommendations
  const userLikedTracks = userLibrary.likedSongs.map(id => sampleTracks[id]).filter(Boolean);
  const userGenres = [...new Set(userLikedTracks.map(track => track.genre))];

  // Recommend tracks from similar genres that user hasn't liked yet
  const recommendations = Object.values(sampleTracks)
    .filter(track =>
      userGenres.includes(track.genre) &&
      !userLibrary.likedSongs.includes(track.id)
    )
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 6);

  res.json(recommendations);
});

// Get home page stats
app.get('/api/home/<USER>', (req, res) => {
  const totalTracks = Object.keys(sampleTracks).length;
  const totalArtists = [...new Set(Object.values(sampleTracks).map(track => track.artist))].length;
  const totalAlbums = [...new Set(Object.values(sampleTracks).map(track => track.album))].length;

  res.json({
    totalTracks: `${totalTracks}+`,
    totalArtists: `${totalArtists}+`,
    totalAlbums: `${totalAlbums}+`,
    totalUsers: '1M+' // Static for demo
  });
});

// Get user's recent activity for home page
app.get('/api/home/<USER>', (req, res) => {
  const recentTracks = userLibrary.recentlyPlayed
    .map(id => sampleTracks[id])
    .filter(Boolean)
    .slice(0, 4);

  const recentPlaylists = userLibrary.playlists
    .sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified))
    .slice(0, 2);

  res.json({
    tracks: recentTracks,
    playlists: recentPlaylists
  });
});

// ===== REUSABLE ENDPOINTS FOR OTHER PAGES =====

// Get all tracks (for search, explore, etc.)
app.get('/api/tracks', (req, res) => {
  const { genre, year, limit } = req.query;
  let tracks = Object.values(sampleTracks);

  // Apply filters
  if (genre) {
    tracks = tracks.filter(track => track.genre.toLowerCase() === genre.toLowerCase());
  }
  if (year) {
    tracks = tracks.filter(track => track.year.toString() === year);
  }
  if (limit) {
    tracks = tracks.slice(0, parseInt(limit));
  }

  res.json(tracks);
});

// Get all artists
app.get('/api/artists', (req, res) => {
  const artists = [...new Set(Object.values(sampleTracks).map(track => track.artist))]
    .map(artistName => {
      const artistTracks = Object.values(sampleTracks).filter(track => track.artist === artistName);
      const totalPlays = artistTracks.reduce((sum, track) => sum + track.plays, 0);

      return {
        name: artistName,
        trackCount: artistTracks.length,
        totalPlays: totalPlays,
        topTrack: artistTracks.sort((a, b) => b.plays - a.plays)[0],
        genres: [...new Set(artistTracks.map(track => track.genre))]
      };
    })
    .sort((a, b) => b.totalPlays - a.totalPlays);

  res.json(artists);
});

// Get all genres
app.get('/api/genres', (req, res) => {
  const genres = [...new Set(Object.values(sampleTracks).map(track => track.genre))]
    .map(genreName => {
      const genreTracks = Object.values(sampleTracks).filter(track => track.genre === genreName);
      return {
        name: genreName,
        trackCount: genreTracks.length,
        topTracks: genreTracks.sort((a, b) => b.plays - a.plays).slice(0, 3)
      };
    });

  res.json(genres);
});

// ===== PROFILE PAGE API ENDPOINTS =====

// Sample user profile data
const userProfile = {
  id: 1,
  username: 'MusicLover2024',
  email: '<EMAIL>',
  displayName: 'Music Lover',
  bio: 'Passionate about discovering new sounds and sharing great music with the world! 🎵',
  avatar: 'imgs/profile-icon-B.png',
  banner: null,
  joinDate: '2024-01-15',
  isPremium: true,
  followers: 12,
  following: 34,
  totalListeningTime: 142, // hours
  favoriteGenres: ['Pop', 'Electronic', 'Indie'],
  location: 'Los Angeles, CA',
  website: 'https://musiclover.com'
};

// Get user profile data
app.get('/api/profile', (req, res) => {
  // Calculate dynamic stats
  const stats = {
    totalPlaylists: userLibrary.playlists.length,
    totalLikedSongs: userLibrary.likedSongs.length,
    totalListeningTime: userProfile.totalListeningTime,
    followers: userProfile.followers,
    following: userProfile.following
  };

  res.json({
    ...userProfile,
    stats
  });
});

// Get user's listening history
app.get('/api/profile/listening-history', (req, res) => {
  const limit = parseInt(req.query.limit) || 20;

  // Generate listening history from recent tracks with timestamps
  const listeningHistory = userLibrary.recentlyPlayed
    .map((trackId, index) => {
      const track = sampleTracks[trackId];
      if (!track) return null;

      // Generate realistic timestamps (last 30 days)
      const daysAgo = Math.floor(index / 2);
      const hoursAgo = Math.floor(Math.random() * 24);
      const timestamp = new Date();
      timestamp.setDate(timestamp.getDate() - daysAgo);
      timestamp.setHours(timestamp.getHours() - hoursAgo);

      return {
        ...track,
        playedAt: timestamp.toISOString(),
        playCount: Math.floor(Math.random() * 10) + 1
      };
    })
    .filter(Boolean)
    .slice(0, limit);

  res.json(listeningHistory);
});

// Get user's top tracks
app.get('/api/profile/top-tracks', (req, res) => {
  const period = req.query.period || 'month'; // week, month, year, all-time
  const limit = parseInt(req.query.limit) || 10;

  // Simulate top tracks based on user's liked songs and recent plays
  const topTracks = userLibrary.likedSongs
    .map(trackId => sampleTracks[trackId])
    .filter(Boolean)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, limit)
    .map((track, index) => ({
      ...track,
      rank: index + 1,
      playCount: Math.floor(Math.random() * 50) + 20,
      period
    }));

  res.json(topTracks);
});

// Get user's top artists
app.get('/api/profile/top-artists', (req, res) => {
  const period = req.query.period || 'month';
  const limit = parseInt(req.query.limit) || 10;

  // Calculate top artists from user's listening data
  const artistPlayCounts = {};
  userLibrary.likedSongs.forEach(trackId => {
    const track = sampleTracks[trackId];
    if (track) {
      artistPlayCounts[track.artist] = (artistPlayCounts[track.artist] || 0) + track.plays;
    }
  });

  const topArtists = Object.entries(artistPlayCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit)
    .map(([artistName, totalPlays], index) => {
      const artistTracks = Object.values(sampleTracks).filter(track => track.artist === artistName);
      return {
        rank: index + 1,
        name: artistName,
        totalPlays,
        trackCount: artistTracks.length,
        topTrack: artistTracks.sort((a, b) => b.plays - a.plays)[0],
        genre: artistTracks[0]?.genre || 'Unknown',
        image: artistTracks[0]?.cover || 'imgs/album-01.png',
        period
      };
    });

  res.json(topArtists);
});

// Get user's genre breakdown
app.get('/api/profile/genres', (req, res) => {
  // Calculate genre listening percentages
  const genreCounts = {};
  let totalPlays = 0;

  userLibrary.likedSongs.forEach(trackId => {
    const track = sampleTracks[trackId];
    if (track) {
      genreCounts[track.genre] = (genreCounts[track.genre] || 0) + track.plays;
      totalPlays += track.plays;
    }
  });

  const genreBreakdown = Object.entries(genreCounts)
    .map(([genre, plays]) => ({
      name: genre,
      percentage: Math.round((plays / totalPlays) * 100),
      playCount: plays,
      trackCount: Object.values(sampleTracks).filter(track => track.genre === genre).length
    }))
    .sort((a, b) => b.percentage - a.percentage);

  res.json(genreBreakdown);
});

// ===== ARTIST PAGE API ENDPOINTS =====

// Get artist information by name
app.get('/api/artist/:name', (req, res) => {
  const artistName = decodeURIComponent(req.params.name);

  // Find artist tracks
  const artistTracks = Object.values(sampleTracks).filter(track =>
    track.artist.toLowerCase() === artistName.toLowerCase()
  );

  if (artistTracks.length === 0) {
    return res.status(404).json({ error: 'Artist not found' });
  }

  // Calculate artist stats
  const totalPlays = artistTracks.reduce((sum, track) => sum + track.plays, 0);
  const topTrack = artistTracks.sort((a, b) => b.plays - a.plays)[0];
  const genres = [...new Set(artistTracks.map(track => track.genre))];
  const albums = [...new Set(artistTracks.map(track => track.album))];

  const artistInfo = {
    name: artistTracks[0].artist,
    bio: `${artistTracks[0].artist} is a talented artist known for their unique sound in ${genres.join(', ')}. With ${artistTracks.length} tracks and over ${totalPlays.toLocaleString()} plays, they continue to captivate audiences worldwide.`,
    image: artistTracks[0].cover,
    verified: true,
    stats: {
      monthlyListeners: Math.floor(totalPlays / 10) + 'K',
      followers: Math.floor(totalPlays / 20) + 'K',
      totalTracks: artistTracks.length,
      totalAlbums: albums.length
    },
    genres,
    topTrack,
    socialLinks: {
      spotify: '#',
      apple: '#',
      youtube: '#',
      instagram: '#',
      twitter: '#'
    }
  };

  res.json(artistInfo);
});

// Get artist's discography
app.get('/api/artist/:name/tracks', (req, res) => {
  const artistName = decodeURIComponent(req.params.name);
  const limit = parseInt(req.query.limit) || 20;
  const sort = req.query.sort || 'popularity'; // popularity, recent, alphabetical

  let artistTracks = Object.values(sampleTracks).filter(track =>
    track.artist.toLowerCase() === artistName.toLowerCase()
  );

  // Apply sorting
  switch (sort) {
    case 'popularity':
      artistTracks.sort((a, b) => b.plays - a.plays);
      break;
    case 'recent':
      artistTracks.sort((a, b) => b.year - a.year);
      break;
    case 'alphabetical':
      artistTracks.sort((a, b) => a.title.localeCompare(b.title));
      break;
  }

  res.json(artistTracks.slice(0, limit));
});

// Get artist's albums
app.get('/api/artist/:name/albums', (req, res) => {
  const artistName = decodeURIComponent(req.params.name);

  const artistTracks = Object.values(sampleTracks).filter(track =>
    track.artist.toLowerCase() === artistName.toLowerCase()
  );

  // Group tracks by album
  const albumsMap = {};
  artistTracks.forEach(track => {
    if (!albumsMap[track.album]) {
      albumsMap[track.album] = {
        title: track.album,
        artist: track.artist,
        year: track.year,
        cover: track.cover,
        genre: track.genre,
        tracks: []
      };
    }
    albumsMap[track.album].tracks.push(track);
  });

  // Convert to array and add album stats
  const albums = Object.values(albumsMap).map(album => ({
    ...album,
    trackCount: album.tracks.length,
    totalPlays: album.tracks.reduce((sum, track) => sum + track.plays, 0),
    duration: album.tracks.length * 3.5 // Approximate duration in minutes
  })).sort((a, b) => b.year - a.year);

  res.json(albums);
});

// Get related artists
app.get('/api/artist/:name/related', (req, res) => {
  const artistName = decodeURIComponent(req.params.name);
  const limit = parseInt(req.query.limit) || 6;

  // Find the artist's genres
  const artistTracks = Object.values(sampleTracks).filter(track =>
    track.artist.toLowerCase() === artistName.toLowerCase()
  );

  if (artistTracks.length === 0) {
    return res.json([]);
  }

  const artistGenres = [...new Set(artistTracks.map(track => track.genre))];

  // Find other artists in similar genres
  const relatedArtists = [...new Set(
    Object.values(sampleTracks)
      .filter(track =>
        track.artist.toLowerCase() !== artistName.toLowerCase() &&
        artistGenres.includes(track.genre)
      )
      .map(track => track.artist)
  )].slice(0, limit).map(relatedArtistName => {
    const relatedTracks = Object.values(sampleTracks).filter(track => track.artist === relatedArtistName);
    const totalPlays = relatedTracks.reduce((sum, track) => sum + track.plays, 0);

    return {
      name: relatedArtistName,
      image: relatedTracks[0]?.cover || 'imgs/album-01.png',
      genre: relatedTracks[0]?.genre || 'Unknown',
      followers: Math.floor(totalPlays / 20) + 'K',
      topTrack: relatedTracks.sort((a, b) => b.plays - a.plays)[0]
    };
  });

  res.json(relatedArtists);
});

// ===== EXPLORE PAGE API ENDPOINTS =====

// Get explore page data
app.get('/api/explore', (req, res) => {
  // Get featured content for explore page
  const featuredTracks = Object.values(sampleTracks)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 8);

  const featuredArtists = [...new Set(Object.values(sampleTracks).map(track => track.artist))]
    .slice(0, 6)
    .map(artistName => {
      const artistTracks = Object.values(sampleTracks).filter(track => track.artist === artistName);
      const totalPlays = artistTracks.reduce((sum, track) => sum + track.plays, 0);
      return {
        name: artistName,
        image: artistTracks[0]?.cover || 'imgs/album-01.png',
        genre: artistTracks[0]?.genre || 'Unknown',
        followers: Math.floor(totalPlays / 20) + 'K',
        topTrack: artistTracks.sort((a, b) => b.plays - a.plays)[0]
      };
    });

  const newReleases = Object.values(sampleTracks)
    .filter(track => track.year >= 2022)
    .sort((a, b) => b.year - a.year)
    .slice(0, 6);

  res.json({
    featuredTracks,
    featuredArtists,
    newReleases,
    stats: {
      totalTracks: Object.keys(sampleTracks).length + 'M+',
      totalArtists: [...new Set(Object.values(sampleTracks).map(track => track.artist))].length + 'M+',
      totalAlbums: [...new Set(Object.values(sampleTracks).map(track => track.album))].length + 'K+'
    }
  });
});

// Get genre breakdown for explore page
app.get('/api/explore/genres', (req, res) => {
  const genres = [...new Set(Object.values(sampleTracks).map(track => track.genre))]
    .map(genreName => {
      const genreTracks = Object.values(sampleTracks).filter(track => track.genre === genreName);
      const totalPlays = genreTracks.reduce((sum, track) => sum + track.plays, 0);

      return {
        name: genreName,
        trackCount: genreTracks.length,
        totalPlays,
        topTracks: genreTracks.sort((a, b) => b.plays - a.plays).slice(0, 3),
        description: getGenreDescription(genreName),
        color: getGenreColor(genreName)
      };
    })
    .sort((a, b) => b.totalPlays - a.totalPlays);

  res.json(genres);
});

// Get trending content for explore
app.get('/api/explore/trending', (req, res) => {
  const period = req.query.period || 'week'; // week, month, year
  const limit = parseInt(req.query.limit) || 10;

  // Simulate trending based on recent plays and popularity
  const trending = Object.values(sampleTracks)
    .filter(track => track.year >= 2020) // Recent tracks
    .sort((a, b) => b.plays - a.plays)
    .slice(0, limit)
    .map((track, index) => ({
      ...track,
      rank: index + 1,
      trend: Math.random() > 0.5 ? 'up' : 'down',
      changePercent: Math.floor(Math.random() * 50) + 1
    }));

  res.json(trending);
});

// Helper functions for genre data
function getGenreDescription(genre) {
  const descriptions = {
    'Pop': 'Catchy melodies and mainstream appeal',
    'Electronic': 'Digital beats and synthesized sounds',
    'Indie': 'Independent and alternative music',
    'Rock': 'Guitar-driven and energetic',
    'Hip Hop': 'Rhythmic and lyrical expression',
    'Jazz': 'Improvisation and complex harmonies',
    'Classical': 'Orchestral and timeless compositions',
    'Country': 'Storytelling and acoustic instruments',
    'R&B': 'Soulful vocals and smooth rhythms',
    'Folk': 'Traditional and acoustic storytelling'
  };
  return descriptions[genre] || 'Discover amazing music';
}

function getGenreColor(genre) {
  const colors = {
    'Pop': '#FF006E',
    'Electronic': '#00E0FF',
    'Indie': '#A7FF4A',
    'Rock': '#FF4646',
    'Hip Hop': '#FFD700',
    'Jazz': '#9B59B6',
    'Classical': '#E74C3C',
    'Country': '#F39C12',
    'R&B': '#E91E63',
    'Folk': '#4CAF50'
  };
  return colors[genre] || '#6F00FF';
}

// ===== CHARTS/TRENDING PAGE API ENDPOINTS =====

// Get charts data
app.get('/api/charts', (req, res) => {
  const period = req.query.period || 'week'; // week, month, year
  const region = req.query.region || 'global'; // global, us, uk, etc.

  // Generate top songs chart
  const topSongs = Object.values(sampleTracks)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 20)
    .map((track, index) => ({
      ...track,
      position: index + 1,
      previousPosition: index + Math.floor(Math.random() * 3) - 1,
      weeksOnChart: Math.floor(Math.random() * 20) + 1,
      peakPosition: Math.max(1, index + 1 - Math.floor(Math.random() * 5))
    }));

  // Generate top artists chart
  const artistStats = {};
  Object.values(sampleTracks).forEach(track => {
    if (!artistStats[track.artist]) {
      artistStats[track.artist] = {
        name: track.artist,
        totalPlays: 0,
        trackCount: 0,
        image: track.cover,
        genre: track.genre
      };
    }
    artistStats[track.artist].totalPlays += track.plays;
    artistStats[track.artist].trackCount += 1;
  });

  const topArtists = Object.values(artistStats)
    .sort((a, b) => b.totalPlays - a.totalPlays)
    .slice(0, 15)
    .map((artist, index) => ({
      ...artist,
      position: index + 1,
      previousPosition: index + Math.floor(Math.random() * 3) - 1,
      followers: Math.floor(artist.totalPlays / 20) + 'K'
    }));

  // Generate top albums chart
  const albumStats = {};
  Object.values(sampleTracks).forEach(track => {
    const albumKey = `${track.album}-${track.artist}`;
    if (!albumStats[albumKey]) {
      albumStats[albumKey] = {
        title: track.album,
        artist: track.artist,
        year: track.year,
        image: track.cover,
        genre: track.genre,
        totalPlays: 0,
        trackCount: 0
      };
    }
    albumStats[albumKey].totalPlays += track.plays;
    albumStats[albumKey].trackCount += 1;
  });

  const topAlbums = Object.values(albumStats)
    .sort((a, b) => b.totalPlays - a.totalPlays)
    .slice(0, 12)
    .map((album, index) => ({
      ...album,
      position: index + 1,
      previousPosition: index + Math.floor(Math.random() * 3) - 1
    }));

  // Generate rising stars (newer tracks with high growth)
  const risingStars = Object.values(sampleTracks)
    .filter(track => track.year >= 2022)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 10)
    .map((track, index) => ({
      ...track,
      position: index + 1,
      growthPercent: Math.floor(Math.random() * 200) + 50, // 50-250% growth
      isNew: track.year === 2024
    }));

  // Featured track (top of charts)
  const featured = topSongs[0] ? {
    ...topSongs[0],
    likes: Math.floor(topSongs[0].plays / 10),
    shares: Math.floor(topSongs[0].plays / 50)
  } : null;

  res.json({
    featured,
    songs: topSongs,
    artists: topArtists,
    albums: topAlbums,
    rising: risingStars,
    metadata: {
      period,
      region,
      lastUpdated: new Date().toISOString(),
      totalTracks: Object.keys(sampleTracks).length,
      totalArtists: Object.keys(artistStats).length,
      totalAlbums: Object.keys(albumStats).length
    }
  });
});

// Get specific chart by type
app.get('/api/charts/:type', (req, res) => {
  const { type } = req.params;
  const limit = parseInt(req.query.limit) || 50;
  const period = req.query.period || 'week';

  let chartData = [];

  switch (type) {
    case 'songs':
      chartData = Object.values(sampleTracks)
        .sort((a, b) => b.plays - a.plays)
        .slice(0, limit)
        .map((track, index) => ({
          ...track,
          position: index + 1,
          previousPosition: index + Math.floor(Math.random() * 3) - 1,
          weeksOnChart: Math.floor(Math.random() * 20) + 1
        }));
      break;

    case 'artists':
      const artistStats = {};
      Object.values(sampleTracks).forEach(track => {
        if (!artistStats[track.artist]) {
          artistStats[track.artist] = {
            name: track.artist,
            totalPlays: 0,
            image: track.cover,
            genre: track.genre
          };
        }
        artistStats[track.artist].totalPlays += track.plays;
      });

      chartData = Object.values(artistStats)
        .sort((a, b) => b.totalPlays - a.totalPlays)
        .slice(0, limit)
        .map((artist, index) => ({
          ...artist,
          position: index + 1,
          followers: Math.floor(artist.totalPlays / 20) + 'K'
        }));
      break;

    case 'rising':
      chartData = Object.values(sampleTracks)
        .filter(track => track.year >= 2022)
        .sort((a, b) => b.plays - a.plays)
        .slice(0, limit)
        .map((track, index) => ({
          ...track,
          position: index + 1,
          growthPercent: Math.floor(Math.random() * 200) + 50
        }));
      break;

    default:
      return res.status(404).json({ error: 'Chart type not found' });
  }

  res.json({
    type,
    period,
    data: chartData,
    total: chartData.length
  });
});

// ===== NOTIFICATION API ENDPOINTS =====

// Sample notification data
const userNotifications = [
  {
    id: 1,
    type: 'new_release',
    title: 'New Release Alert',
    message: 'The Weeknd just released a new album "Dawn FM"',
    icon: 'fas fa-music',
    time: '2 hours ago',
    unread: true,
    actionUrl: '/artist.html?artist=The Weeknd',
    priority: 'high',
    category: 'music'
  },
  {
    id: 2,
    type: 'playlist_update',
    title: 'Playlist Updated',
    message: 'Your "Chill Vibes" playlist has been updated with 5 new tracks',
    icon: 'fas fa-list-music',
    time: '4 hours ago',
    unread: true,
    actionUrl: '/playlist.html?id=1',
    priority: 'medium',
    category: 'playlist'
  },
  {
    id: 3,
    type: 'friend_activity',
    title: 'Friend Activity',
    message: 'Sarah liked your playlist "Study Focus"',
    icon: 'fas fa-heart',
    time: '6 hours ago',
    unread: false,
    actionUrl: '/profile.html?user=sarah',
    priority: 'low',
    category: 'social'
  },
  {
    id: 4,
    type: 'system',
    title: 'Account Security',
    message: 'Your password was successfully updated',
    icon: 'fas fa-shield-alt',
    time: '1 day ago',
    unread: false,
    actionUrl: '/settings.html',
    priority: 'high',
    category: 'security'
  },
  {
    id: 5,
    type: 'recommendation',
    title: 'New Recommendations',
    message: 'We found 12 new songs you might like based on your listening history',
    icon: 'fas fa-magic',
    time: '2 days ago',
    unread: false,
    actionUrl: '/explore.html',
    priority: 'medium',
    category: 'discovery'
  },
  {
    id: 6,
    type: 'achievement',
    title: 'Achievement Unlocked',
    message: 'You\'ve listened to 100 hours of music this month!',
    icon: 'fas fa-trophy',
    time: '3 days ago',
    unread: false,
    actionUrl: '/profile.html',
    priority: 'medium',
    category: 'achievement'
  },
  {
    id: 7,
    type: 'subscription',
    title: 'Subscription Reminder',
    message: 'Your Premium subscription will renew in 3 days',
    icon: 'fas fa-crown',
    time: '1 week ago',
    unread: false,
    actionUrl: '/subscribe.html',
    priority: 'medium',
    category: 'billing'
  }
];

// Get all notifications
app.get('/api/notifications', (req, res) => {
  const { filter, limit, unread_only } = req.query;
  let notifications = [...userNotifications];

  // Filter by category
  if (filter && filter !== 'all') {
    notifications = notifications.filter(n => n.category === filter);
  }

  // Filter by unread status
  if (unread_only === 'true') {
    notifications = notifications.filter(n => n.unread);
  }

  // Apply limit
  if (limit) {
    notifications = notifications.slice(0, parseInt(limit));
  }

  // Sort by newest first (assuming higher ID = newer)
  notifications.sort((a, b) => b.id - a.id);

  res.json({
    notifications,
    total: notifications.length,
    unreadCount: userNotifications.filter(n => n.unread).length,
    categories: ['all', 'music', 'playlist', 'social', 'security', 'discovery', 'achievement', 'billing']
  });
});

// Get notification statistics
app.get('/api/notifications/stats', (req, res) => {
  const stats = {
    total: userNotifications.length,
    unread: userNotifications.filter(n => n.unread).length,
    byCategory: {
      music: userNotifications.filter(n => n.category === 'music').length,
      playlist: userNotifications.filter(n => n.category === 'playlist').length,
      social: userNotifications.filter(n => n.category === 'social').length,
      security: userNotifications.filter(n => n.category === 'security').length,
      discovery: userNotifications.filter(n => n.category === 'discovery').length,
      achievement: userNotifications.filter(n => n.category === 'achievement').length,
      billing: userNotifications.filter(n => n.category === 'billing').length
    },
    byPriority: {
      high: userNotifications.filter(n => n.priority === 'high').length,
      medium: userNotifications.filter(n => n.priority === 'medium').length,
      low: userNotifications.filter(n => n.priority === 'low').length
    }
  };

  res.json(stats);
});

// Mark notification as read
app.patch('/api/notifications/:id/read', (req, res) => {
  const notificationId = parseInt(req.params.id);
  const notification = userNotifications.find(n => n.id === notificationId);

  if (!notification) {
    return res.status(404).json({ error: 'Notification not found' });
  }

  notification.unread = false;
  res.json({ success: true, notification });
});

// Mark all notifications as read
app.patch('/api/notifications/read-all', (req, res) => {
  const { category } = req.body;

  let updatedCount = 0;
  userNotifications.forEach(notification => {
    if (notification.unread && (!category || notification.category === category)) {
      notification.unread = false;
      updatedCount++;
    }
  });

  res.json({
    success: true,
    updatedCount,
    message: `${updatedCount} notifications marked as read`
  });
});

// Delete notification
app.delete('/api/notifications/:id', (req, res) => {
  const notificationId = parseInt(req.params.id);
  const index = userNotifications.findIndex(n => n.id === notificationId);

  if (index === -1) {
    return res.status(404).json({ error: 'Notification not found' });
  }

  const deletedNotification = userNotifications.splice(index, 1)[0];
  res.json({ success: true, deletedNotification });
});

// Create new notification (for testing/admin)
app.post('/api/notifications', (req, res) => {
  const { type, title, message, category, priority, actionUrl } = req.body;

  if (!title || !message) {
    return res.status(400).json({ error: 'Title and message are required' });
  }

  const newNotification = {
    id: Math.max(...userNotifications.map(n => n.id), 0) + 1,
    type: type || 'system',
    title,
    message,
    icon: getIconForType(type || 'system'),
    time: 'Just now',
    unread: true,
    actionUrl: actionUrl || null,
    priority: priority || 'medium',
    category: category || 'system'
  };

  userNotifications.unshift(newNotification);
  res.status(201).json(newNotification);
});

// Helper function to get icon for notification type
function getIconForType(type) {
  const iconMap = {
    new_release: 'fas fa-music',
    playlist_update: 'fas fa-list-music',
    friend_activity: 'fas fa-heart',
    system: 'fas fa-cog',
    recommendation: 'fas fa-magic',
    achievement: 'fas fa-trophy',
    subscription: 'fas fa-crown',
    security: 'fas fa-shield-alt'
  };
  return iconMap[type] || 'fas fa-bell';
}

app.listen(PORT, () => {
  console.log(`🎵 Banshee Music API server running on port ${PORT}`);
  console.log(`📡 Available endpoints:`);
  console.log(`   Library: http://localhost:${PORT}/api/library/*`);
  console.log(`   Home: http://localhost:${PORT}/api/home/<USER>
  console.log(`   Notifications: http://localhost:${PORT}/api/notifications/*`);
});
